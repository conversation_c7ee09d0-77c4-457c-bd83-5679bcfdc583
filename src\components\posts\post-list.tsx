'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePosts } from '@/hooks/use-posts';
import { QueryPostDto, PostStatus } from '../../../packages/validation';
import { formatDate } from '@/lib/utils';

interface PostListProps {
  initialParams?: QueryPostDto;
  showFilters?: boolean;
}

export function PostList({ initialParams, showFilters = true }: PostListProps) {
  const [params, setParams] = useState<QueryPostDto>(initialParams || {
    page: 1,
    limit: 10,
    status: 'PUBLISHED',
  });

  const { data, isLoading, error } = usePosts(params);

  const handleSearch = (query: string) => {
    setParams(prev => ({ ...prev, q: query, page: 1 }));
  };

  const handleStatusFilter = (status: PostStatus | undefined) => {
    setParams(prev => ({ ...prev, status, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setParams(prev => ({ ...prev, page }));
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 h-6 w-3/4 mb-2 rounded"></div>
            <div className="bg-gray-200 h-4 w-full mb-1 rounded"></div>
            <div className="bg-gray-200 h-4 w-2/3 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">加载文章失败</p>
      </div>
    );
  }

  if (!data || data.data.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">暂无文章</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {showFilters && (
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="搜索文章..."
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          <select
            value={params.status || ''}
            onChange={(e) => {
              const value = e.target.value;
              handleStatusFilter(value === '' ? undefined : value as PostStatus);
            }}
            className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">所有状态</option>
            <option value="PUBLISHED">已发布</option>
            <option value="DRAFT">草稿</option>
            <option value="ARCHIVED">已归档</option>
          </select>
        </div>
      )}

      <div className="space-y-6">
        {data.data.map((post) => (
          <article key={post.id} className="border-b border-gray-200 pb-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <Link
                  href={`/posts/${post.slug}`}
                  className="block group"
                >
                  <h2 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 mb-2">
                    {post.title}
                  </h2>
                </Link>
                
                <p className="text-gray-600 mb-3 line-clamp-3">
                  {post.summary}
                </p>

                <div className="flex items-center text-sm text-gray-500 space-x-4">
                  <span>{formatDate(post.createdAt)}</span>
                  <span>阅读时间 {post.readingTime} 分钟</span>
                  {post.status !== 'PUBLISHED' && (
                    <span className={`px-2 py-1 rounded text-xs ${
                      post.status === 'DRAFT' 
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {post.status === 'DRAFT' ? '草稿' : '已归档'}
                    </span>
                  )}
                </div>
              </div>

              {post.coverUrl && (
                <div className="ml-4 flex-shrink-0">
                  <img
                    src={post.coverUrl}
                    alt={post.title}
                    className="w-24 h-24 object-cover rounded-md"
                  />
                </div>
              )}
            </div>
          </article>
        ))}
      </div>

      {/* Pagination */}
      {data.total > data.limit && (
        <div className="flex justify-center space-x-2">
          <button
            onClick={() => handlePageChange(data.page - 1)}
            disabled={data.page <= 1}
            className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            上一页
          </button>
          
          <span className="px-3 py-2 text-gray-600">
            第 {data.page} 页，共 {Math.ceil(data.total / data.limit)} 页
          </span>
          
          <button
            onClick={() => handlePageChange(data.page + 1)}
            disabled={data.page >= Math.ceil(data.total / data.limit)}
            className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            下一页
          </button>
        </div>
      )}
    </div>
  );
}
