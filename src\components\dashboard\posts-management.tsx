'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePosts, useDeletePost, usePublishPost, useArchivePost } from '@/hooks/use-posts';
import { QueryPostDto, PostStatus } from '../../../packages/validation';
import { formatDate } from '@/lib/utils';

export function PostsManagement() {
  const [params, setParams] = useState<QueryPostDto>({
    page: 1,
    limit: 20,
  });
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);

  const { data, isLoading, error } = usePosts(params);
  const deletePost = useDeletePost();
  const publishPost = usePublishPost();
  const archivePost = useArchivePost();

  const handleStatusFilter = (status: PostStatus | undefined) => {
    setParams(prev => ({ ...prev, status, page: 1 }));
  };

  const handleSearch = (query: string) => {
    setParams(prev => ({ ...prev, q: query, page: 1 }));
  };

  const handleSelectPost = (postId: string) => {
    setSelectedPosts(prev =>
      prev.includes(postId)
        ? prev.filter(id => id !== postId)
        : [...prev, postId]
    );
  };

  const handleSelectAll = () => {
    if (!data?.data) return;
    
    if (selectedPosts.length === data.data.length) {
      setSelectedPosts([]);
    } else {
      setSelectedPosts(data.data.map(post => post.id));
    }
  };

  const handleDelete = async (postId: string) => {
    if (confirm('确定要删除这篇文章吗？此操作不可恢复。')) {
      try {
        await deletePost.mutateAsync(postId);
        setSelectedPosts(prev => prev.filter(id => id !== postId));
      } catch (error) {
        alert('删除失败，请重试');
      }
    }
  };

  const handlePublish = async (postId: string) => {
    try {
      await publishPost.mutateAsync(postId);
    } catch (error) {
      alert('发布失败，请重试');
    }
  };

  const handleArchive = async (postId: string) => {
    try {
      await archivePost.mutateAsync(postId);
    } catch (error) {
      alert('归档失败，请重试');
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="bg-gray-200 h-8 w-48 mb-4 rounded"></div>
          <div className="bg-gray-200 h-64 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">加载文章失败</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">文章管理</h1>
        <Link
          href="/write"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          写新文章
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="搜索文章..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          <select
            value={params.status || ''}
            onChange={(e) => {
              const value = e.target.value;
              handleStatusFilter(value === '' ? undefined : value as PostStatus);
            }}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">所有状态</option>
            <option value="PUBLISHED">已发布</option>
            <option value="DRAFT">草稿</option>
            <option value="ARCHIVED">已归档</option>
          </select>
        </div>
      </div>

      {/* Posts Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {data?.data && data.data.length > 0 ? (
          <>
            <div className="px-4 py-3 border-b border-gray-200">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedPosts.length === data.data.length}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-600">
                  {selectedPosts.length > 0 && `已选择 ${selectedPosts.length} 篇文章`}
                </span>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      选择
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      标题
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      创建时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {data.data.map((post) => (
                    <tr key={post.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedPosts.includes(post.id)}
                          onChange={() => handleSelectPost(post.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <Link
                            href={`/posts/${post.slug}`}
                            className="text-sm font-medium text-gray-900 hover:text-blue-600"
                          >
                            {post.title}
                          </Link>
                          <p className="text-sm text-gray-500 truncate max-w-xs">
                            {post.summary}
                          </p>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          post.status === 'PUBLISHED'
                            ? 'bg-green-100 text-green-800'
                            : post.status === 'DRAFT'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {post.status === 'PUBLISHED' ? '已发布' : 
                           post.status === 'DRAFT' ? '草稿' : '已归档'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(post.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <Link
                          href={`/dashboard/posts/${post.id}/edit`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          编辑
                        </Link>
                        {post.status === 'DRAFT' && (
                          <button
                            onClick={() => handlePublish(post.id)}
                            className="text-green-600 hover:text-green-900"
                          >
                            发布
                          </button>
                        )}
                        {post.status === 'PUBLISHED' && (
                          <button
                            onClick={() => handleArchive(post.id)}
                            className="text-yellow-600 hover:text-yellow-900"
                          >
                            归档
                          </button>
                        )}
                        <button
                          onClick={() => handleDelete(post.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          删除
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {data.total > data.limit && (
              <div className="px-6 py-3 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-700">
                    显示 {(data.page - 1) * data.limit + 1} 到 {Math.min(data.page * data.limit, data.total)} 条，
                    共 {data.total} 条
                  </span>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setParams(prev => ({ ...prev, page: prev.page! - 1 }))}
                      disabled={data.page <= 1}
                      className="px-3 py-1 border border-gray-300 rounded disabled:opacity-50"
                    >
                      上一页
                    </button>
                    <button
                      onClick={() => setParams(prev => ({ ...prev, page: prev.page! + 1 }))}
                      disabled={data.page >= Math.ceil(data.total / data.limit)}
                      className="px-3 py-1 border border-gray-300 rounded disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">暂无文章</p>
          </div>
        )}
      </div>
    </div>
  );
}
